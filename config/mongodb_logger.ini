[main]
; Enable or disable MongoDB logging
enabled=true

; MongoDB connection settings
host=*************
port=27017
database=haraka_email_logs

; Authentication (leave empty if no auth required)
username=
password=
auth_database=admin

; Connection options
connection_timeout=5000
socket_timeout=30000
max_pool_size=10

; Logging configuration
log_all_transactions=true
log_headers=true
log_body=false
sanitize_headers=true

; Data retention (in days, 0 = keep forever)
retention_days=365

; Performance settings
batch_size=100
batch_timeout=5000
enable_indexing=true

[collections]
; Collection names for different log types
transactions=email_transactions
connections=email_connections
errors=email_errors
stats=email_stats

[privacy]
; Privacy protection settings (integrate with existing header stripping)
strip_private_ips=true
strip_internal_headers=true
anonymize_local_addresses=false

; Headers to always exclude from logging
exclude_headers=received-spf,authentication-results,x-originating-ip

[logging]
; Plugin logging level
level=info
log_connection_details=true
log_performance_metrics=true
log_errors=true

[indexing]
; Automatic index creation
create_indexes_on_startup=true
index_timestamp=true
index_transaction_id=true
index_sender=true
index_recipient=true
index_source_ip=true
index_status=true
