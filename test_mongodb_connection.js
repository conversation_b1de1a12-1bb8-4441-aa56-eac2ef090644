#!/usr/bin/env node

// MongoDB Connection Test Script
const { MongoClient } = require('mongodb');

const hosts_to_test = [
    '*************',
    'localhost',
    '127.0.0.1',
    '**********',  // Docker gateway
    'host.docker.internal'
];

async function testConnection(host, port = 27017) {
    const connection_string = `mongodb://${host}:${port}/test`;
    console.log(`\nTesting connection to: ${connection_string}`);
    
    try {
        const client = new MongoClient(connection_string, {
            connectTimeoutMS: 5000,
            socketTimeoutMS: 5000,
            serverSelectionTimeoutMS: 5000
        });
        
        await client.connect();
        console.log(`✅ SUCCESS: Connected to MongoDB at ${host}:${port}`);
        await client.close();
        return true;
    } catch (error) {
        console.log(`❌ FAILED: ${error.message}`);
        return false;
    }
}

async function runTests() {
    console.log('MongoDB Connection Test');
    console.log('======================');
    
    for (const host of hosts_to_test) {
        await testConnection(host);
    }
    
    console.log('\nTest completed.');
}

runTests().catch(console.error);
