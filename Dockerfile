FROM instrumentisto/haraka

# Set working directory
WORKDIR /etc/haraka

# Copy package.json first for better Docker layer caching
COPY package.json ./

# Install MongoDB npm module and any other dependencies
RUN npm install

# Copy your custom configuration and plugins
COPY config ./config
COPY plugins ./plugins

# Ensure proper permissions
RUN chown -R haraka:haraka /etc/haraka/config /etc/haraka/plugins

# Expose SMTP ports
EXPOSE 25
EXPOSE 587

# Use the default Haraka entrypoint
CMD ["haraka", "-c", "/etc/haraka"]
